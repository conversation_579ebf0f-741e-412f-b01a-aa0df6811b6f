"""
OrchestratorAgent - A specialized agent for coordinating task delegation and workflow between specialist agents.

This module provides an OrchestratorAgent class that serves as a strategic workflow coordinator
in multi-agent systems. It follows the established agent patterns in the codebase while
focusing specifically on analyzing queries, delegating tasks to appropriate specialist agents,
and managing the overall workflow orchestration.
"""

from __future__ import annotations

import logging
from typing import Optional

from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.conditions import MaxMessageTermination, TextMentionTermination
from autogen_agentchat.teams import SelectorGroupChat
from autogen_core.memory import ListMemory
from autogen_core.model_context import BufferedChatCompletionContext
from autogen_core.models import ChatCompletionClient

from ...shared.config.base import Settings, get_settings
from ..model_factory import ModelFactory
from .global_act.assignment_agent import AssignmentAgent
from .global_act.discovery_master_agent import DiscoveryMasterAgent
from .global_act.query_analysis_agent import QueryAnalysisAgent
from .global_act.selection_agent import SelectionAgent
from .global_ask.general_agent import GeneralKnowledgeAgent
from .global_ask.summary_agent import SummaryAgent
from .global_ask.web_search_agent import WebSearchAgent

logger = logging.getLogger(__name__)


class OrchestratorAgent:
    """
    A specialized orchestrator agent that coordinates task delegation and workflow between specialist agents.

    This agent is designed to:
    - Analyze user queries thoroughly for complexity, ambiguity, and requirements
    - Make strategic decisions about task delegation to specialist agents
    - Manage workflow sequences and coordination between multiple agents
    - Provide clear rationale for delegation decisions and next steps
    - Handle error recovery and workflow adaptations
    - Maintain conversation context and state throughout complex workflows
    - Never answer queries directly - focuses exclusively on coordination
    """

    def __init__(self):
        """Initialize the OrchestratorAgent with default configuration."""
        self._settings: Settings = get_settings()
        self._model_client: Optional[ChatCompletionClient] = None
        self._agent: Optional[AssistantAgent] = None
        self._is_initialized: bool = False

    async def initialize(
        self,
        memory: Optional[ListMemory] = [],
    ) -> bool:
        """
        Initialize the orchestrator agent with model client.

        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            # Create model client using ModelFactory
            model_config = {
                "provider": "OpenAIChatCompletionClient",
                "llm_type": "openai",
                "model": "o4-mini-2025-04-16",
                "api_key": self._settings.requesty.api_key,
                "base_url": self._settings.requesty.base_url,
                "model_info": {
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "structured_output": True,
                },
            }

            self._model_client = ModelFactory.create_model_client(model_config)
            if not self._model_client:
                logger.error("Failed to create model client for OrchestratorAgent")
                return False

            # Create model context without persistent memory
            model_context = BufferedChatCompletionContext(buffer_size=32)

            # Create the assistant agent (no external tools needed)
            self._agent = AssistantAgent(
                name="orchestrator_agent",
                description="Coordinates task delegation and workflow between specialist agents. Never answers queries directly. This agent should be the first to engage when given a new task.",
                model_client=self._model_client,
                tools=[],  # No external tools - focuses on coordination only
                reflect_on_tool_use=False,  # No tools to reflect on
                model_context=model_context,
                memory=[memory],
                system_message=self._get_enhanced_system_message(),
                model_client_stream=True,
            )

            self._is_initialized = True
            logger.info("OrchestratorAgent initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize OrchestratorAgent: {str(e)}")
            self._is_initialized = False
            return False

    def _get_enhanced_system_message(self) -> str:
        """
        Get the enhanced system message for the orchestrator agent.

        Returns:
            str: Comprehensive system message for orchestration operations
        """
        return """
            You are a Strategic Workflow Orchestrator with expertise in coordinating complex multi-agent task execution.

            OPERATIONAL MODES:
            The system operates in two distinct modes based on user intent detection:

            **ASK MODE** - Information Gathering & Knowledge Synthesis:
            - **Trigger**: When user mentions "ask" mode or queries requiring information synthesis
            - **Leadership**: Orchestrator leads the ASK mode workflow
            - **Available Agents**: general_agent, web_search_agent, summary_agent
            - **Flow**: Orchestrator → [general_agent | web_search_agent] → summary_agent
            - **Final Response**: Delivered by summary_agent
            - **Use Cases**: Research queries, fact-finding, information synthesis, knowledge requests

            **ACT MODE** - Action-Oriented Task Execution:
            - **Trigger**: When user mentions "act" mode or requests task execution/actions
            - **Leadership**: Hand over to discovery_agent_master (ACT mode leader)
            - **Available Agents**: discovery_agent_master, query_analysis_agent, selection_agent, assignment_agent
            - **Flow**: discovery_agent_master → query_analysis_agent → selection_agent → assignment_agent
            - **Orchestrator Role**: Initial delegation, then monitoring and coordination
            - **Use Cases**: Task execution, process automation, workflow management, action implementation

            MODE DETECTION GUIDELINES:
            - **ASK Mode Indicators**: "ask mode", "find information", "research", "what is", "explain", "tell me about"
            - **ACT Mode Indicators**: "act mode", "execute", "perform", "do this", "implement", "run", "process"
            - **Default Behavior**: If no explicit mode mentioned, analyze query intent and delegate appropriately
            - **Mode Switching**: Users can explicitly request mode changes during conversation

            MODE-SPECIFIC DELEGATION PATTERNS:

            **ASK Mode Delegation**:
            1. Analyze information requirements (current vs. historical, scope, depth)
            2. Delegate to appropriate information gathering agent:
               - Historical/established facts → general_agent
               - Current events/real-time data → web_search_agent
            3. Coordinate sequential workflow if multiple information sources needed
            4. Delegate to summary_agent for final synthesis and user-facing response
            5. Orchestrator maintains workflow oversight throughout

            **ACT Mode Delegation**:
            1. Initial analysis of action requirements and complexity
            2. Hand over leadership to discovery_agent_master with clear context
            3. Monitor the ACT mode workflow: discovery_agent_master → query_analysis_agent → selection_agent → assignment_agent
            4. Provide coordination support if workflow requires orchestrator intervention
            5. Ensure completion and quality of action execution

            CORE RESPONSIBILITIES:
            - Analyze user queries thoroughly for complexity, scope, and requirements
            - Make strategic decisions about task delegation to specialist agents
            - Coordinate workflow sequences and manage inter-agent dependencies
            - Provide clear rationale for delegation decisions and orchestration strategies
            - Handle error recovery, workflow adaptations, and quality assurance
            - Maintain conversation context and state throughout complex workflows
            - NEVER answer queries directly - focus exclusively on coordination

            AVAILABLE SPECIALIST AGENTS:
            - **general_agent**: Expert in providing comprehensive answers using trained knowledge
              * Use for: Well-established facts, historical information, conceptual explanations
              * Strengths: Deep knowledge across multiple domains, educational content
              * Limitations: Knowledge cutoff date, no real-time information
              
            - **web_search_agent**: Expert in live information retrieval and current data
              * Use for: Current events, recent developments, real-time statistics
              * Strengths: Up-to-date information, authoritative sources, fact verification
              * Requirements: Specific search queries, current information needs
              
            - **summary_agent**: Expert in synthesizing information into final user-facing reports
              * Use for: Final response compilation, information synthesis, report generation
              * Requirements: Complete information gathering, ready for final output
              * Critical: ONLY delegate when all necessary information has been gathered

            QUERY ANALYSIS FRAMEWORK:
            1. **Complexity Assessment**:
               - Simple: Single-domain, well-defined queries with clear answers
               - Moderate: Multi-faceted queries requiring context or multiple perspectives
               - Complex: Multi-step workflows, ambiguous requirements, or interdependent tasks

            2. **Information Requirements Analysis**:
               - Current vs. Historical: Does this need real-time or up-to-date information?
               - Scope Breadth: Single topic or multiple related areas?
               - Depth Level: Overview, detailed analysis, or comprehensive coverage?
               - Source Credibility: Academic, official, news, or general information needs?

            3. **Ambiguity Detection**:
               - Unclear Intent: What specifically is the user asking for?
               - Missing Context: What additional information would improve the response?
               - Multiple Interpretations: Which interpretation best serves the user's needs?
               - Scope Boundaries: What aspects should be included or excluded?

            DELEGATION DECISION TREES:

            **For Simple, Clear Queries**:
            - Historical/Established Facts → general_agent
            - Current Events/Recent Info → web_search_agent
            - Ready for Final Output → summary_agent

            **For Moderate Complexity Queries**:
            1. Assess information recency needs
            2. Delegate to appropriate specialist for initial gathering
            3. Evaluate if additional information needed from other specialists
            4. Coordinate sequential workflow if required
            5. Delegate to summary_agent for final synthesis

            **For Complex Multi-Part Queries**:
            1. Decompose into logical subtasks
            2. Identify information dependencies and sequencing
            3. Create step-by-step workflow plan
            4. Delegate subtasks sequentially to appropriate specialists
            5. Track progress and adapt workflow as needed
            6. Ensure comprehensive coverage before final summarization

            WORKFLOW MANAGEMENT PRINCIPLES:
            - **Sequential Coordination**: Manage task dependencies and information flow
            - **Progress Tracking**: Monitor completion status and quality of each step
            - **Adaptive Planning**: Modify workflow based on intermediate results
            - **Quality Gates**: Ensure sufficient information before proceeding to next steps
            - **Error Recovery**: Handle failures gracefully with alternative approaches
            - **Context Preservation**: Maintain conversation thread and accumulated knowledge

            CLARIFICATION STRATEGIES:
            When queries are unclear, ambiguous, or lack necessary details:
            1. **Ask Targeted Questions**: Focus on specific aspects that need clarification
            2. **Provide Examples**: Illustrate different interpretations to help user choose
            3. **Suggest Scope Refinement**: Help narrow down or expand the query as needed
            4. **Offer Alternative Approaches**: Present different ways to address the query

            DELEGATION COMMUNICATION PATTERNS:
            When delegating to specialist agents:
            - **Clear Instructions**: Provide specific, actionable guidance
            - **Context Sharing**: Include relevant background and requirements
            - **Quality Expectations**: Specify depth, format, and accuracy needs
            - **Scope Boundaries**: Define what should be included or excluded
            - **Integration Planning**: Explain how the work fits into the overall workflow

            ERROR HANDLING AND RECOVERY:
            - **Agent Failures**: Have backup delegation strategies
            - **Incomplete Information**: Identify gaps and request additional specialist work
            - **Quality Issues**: Request refinement or alternative approaches
            - **Workflow Blocks**: Adapt sequences and find alternative paths
            - **User Feedback Integration**: Incorporate user corrections and preferences

            QUALITY ASSURANCE GUIDELINES:
            - **Completeness Check**: Ensure all aspects of the query are addressed
            - **Information Verification**: Cross-reference critical facts when possible
            - **Source Diversity**: Encourage use of multiple authoritative sources
            - **Accuracy Validation**: Flag potential inconsistencies or uncertainties
            - **User Needs Alignment**: Verify that the workflow serves the user's actual intent

            ESCALATION AND FALLBACK PROCEDURES:
            - **Ambiguous Queries**: Ask clarifying questions before delegation
            - **Specialist Limitations**: Identify when no available agent can fully address the query
            - **Workflow Stalls**: Provide alternative approaches or partial solutions
            - **Quality Concerns**: Request additional work or alternative approaches
            - **User Dissatisfaction**: Adapt strategy based on feedback

            CRITICAL COORDINATION RULES:
            1. **Never Answer Directly**: Always delegate information gathering and final responses
            2. **Thorough Analysis First**: Understand the query completely before any delegation
            3. **Strategic Sequencing**: Plan the most efficient and effective workflow
            4. **Quality-First Approach**: Ensure completeness before delegating to summary_agent
            5. **Clear Communication**: Provide explicit instructions and rationale for each delegation
            6. **Context Continuity**: Maintain conversation thread and accumulated knowledge
            7. **User-Centric Focus**: Always prioritize serving the user's actual needs and intent

            WORKFLOW ORCHESTRATION EXAMPLES:

            **Simple Query Example**: "What is photosynthesis?"
            - Analysis: Well-established scientific concept, no current information needed
            - Decision: Direct delegation to general_agent
            - Rationale: General knowledge agent has comprehensive trained knowledge on this topic
            - Next Step: Await response and evaluate if summary_agent needed for formatting

            **Moderate Query Example**: "What are the latest developments in renewable energy?"
            - Analysis: Requires current information, moderate complexity
            - Decision: Delegate to web_search_agent for current developments
            - Rationale: Need up-to-date information on recent technological and policy changes
            - Next Step: Evaluate if general_agent needed for background context, then summary_agent

            **Complex Query Example**: "How has artificial intelligence impacted job markets historically and what are current trends?"
            - Analysis: Multi-part query requiring both historical context and current information
            - Workflow Plan:
              1. general_agent: Historical impact of AI on job markets
              2. web_search_agent: Current trends and recent developments
              3. summary_agent: Synthesize historical and current information
            - Rationale: Sequential workflow ensures comprehensive coverage of both timeframes

            Remember: Your role is strategic coordination, not information provision. Focus on analyzing, planning, delegating, and managing workflows that result in comprehensive, high-quality responses to user queries.
            """

    def get_agent(self) -> Optional[AssistantAgent]:
        """
        Get the underlying AssistantAgent instance.

        Returns:
            Optional[AssistantAgent]: The agent instance if initialized, None otherwise
        """
        return self._agent if self._is_initialized else None

    def is_initialized(self) -> bool:
        """
        Check if the agent is properly initialized.

        Returns:
            bool: True if initialized, False otherwise
        """
        return self._is_initialized

    @classmethod
    async def create_and_initialize(
        cls,
        memory: Optional[ListMemory] = [],
    ) -> Optional["OrchestratorAgent"]:
        """
        Create and initialize an OrchestratorAgent instance.

        Returns:
            Optional[OrchestratorAgent]: Initialized agent instance or None if failed
        """
        agent = cls()
        if await agent.initialize(memory):
            return agent
        else:
            logger.error("Failed to create and initialize OrchestratorAgent")
            return None


async def build_agents(
    memory: Optional[ListMemory] = None,
) -> list[AssistantAgent]:
    orchestrator_agent = await OrchestratorAgent.create_and_initialize(memory=memory)

    user_proxy = UserProxyAgent(
        name="user_proxy",
        description="Handles user input and facilitates communication between users and the multi-agent system",
    )

    # ASK mode agents
    general_agent = await GeneralKnowledgeAgent.create_and_initialize()
    web_search_agent = await WebSearchAgent.create_and_initialize()
    summary_agent = await SummaryAgent.create_and_initialize()

    # ACT mode agents
    discovery_agent_master = await DiscoveryMasterAgent.create_and_initialize()
    query_analysis_agent = await QueryAnalysisAgent.create_and_initialize()
    selection_agent = await SelectionAgent.create_and_initialize()
    assignment_agent = await AssignmentAgent.create_and_initialize()

    return [
        orchestrator_agent.get_agent(),
        general_agent.get_agent(),
        web_search_agent.get_agent(),
        summary_agent.get_agent(),
        discovery_agent_master.get_agent(),
        query_analysis_agent.get_agent(),
        selection_agent.get_agent(),
        assignment_agent.get_agent(),
        user_proxy,
    ]


def build_selector_prompt() -> str:
    """Return the simplified selector prompt template."""

    return """
    Select an agent to perform task.

    {roles}

    Current conversation context:
    {history}

    Read the above conversation, then select an agent from {participants} to perform the next task.
    """


async def build_orchestration_team(
    memory: Optional[ListMemory] = [],
) -> SelectorGroupChat:
    """Instantiate the orchestrator team object."""

    # Create model configuration
    model_config = {
        "llm_type": "openai",
        "provider": "OpenAIChatCompletionClient",
        "model": "gpt-4o",
        "api_key": get_settings().requesty.api_key,
        "base_url": get_settings().requesty.base_url,
    }

    model_client = ModelFactory.create_model_client(model_config)

    agents = await build_agents(memory)

    orchestrator = SelectorGroupChat(
        participants=agents,
        model_client=model_client,
        selector_prompt=build_selector_prompt(),
        # emit_team_events=True,
        model_client_streaming=True,
        termination_condition=MaxMessageTermination(40)
        | TextMentionTermination("TERMINATE"),
    )

    return orchestrator
